<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reading extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscriber_id',
        'collector_id',
        'previous_reading',
        'current_reading',
        'image_path',
        'status',
        'reviewed_by',
        'review_note',
    ];

    protected $casts = [
        'previous_reading' => 'float',
        'current_reading' => 'float',
        'status' => 'string',
    ];

    /**
     * العلاقة مع المشترك
     */
    public function subscriber()
    {
        return $this->belongsTo(Subscriber::class);
    }

    /**
     * العلاقة مع المحصل
     */
    public function collector()
    {
        return $this->belongsTo(User::class, 'collector_id');
    }

    /**
     * العلاقة مع المراجع
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * العلاقة مع الفاتورة
     */
    public function bill()
    {
        return $this->hasOne(Bill::class);
    }

    /**
     * حساب الاستهلاك (فرق القراءتين)
     */
    public function getConsumptionAttribute()
    {
        return $this->current_reading - $this->previous_reading;
    }

    /**
     * تحقق من أن القراءة معلقة
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * تحقق من أن القراءة تمت الموافقة عليها
     */
    public function isApproved()
    {
        return $this->status === 'approved';
    }

    /**
     * تحقق من أن القراءة تم رفضها
     */
    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    /**
     * Scope للقراءات المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }
}
