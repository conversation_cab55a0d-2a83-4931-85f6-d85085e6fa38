<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'user_id',
        'username',
        'name',
        'phone',
        'password',
        'role',
        'area',
        'status',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'password' => 'hashed',
    ];

    // العلاقة مع القراءات (قراءات قام بها المحصل)
    public function readings()
    {
        return $this->hasMany(Reading::class, 'collector_id');
    }

    // العلاقة مع القراءات المراجعة (قراءات راجعها المراجع)
    public function reviewedReadings()
    {
        return $this->hasMany(Reading::class, 'reviewed_by');
    }

    // العلاقة مع المناطق (للمحصلين)
    public function zones()
    {
        return $this->hasMany(Zone::class, 'collector_id');
    }

    // تحقق من الدور
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isCollector()
    {
        return $this->role === 'collector';
    }

    public function isReviewer()
    {
        return $this->role === 'reviewer';
    }
}
