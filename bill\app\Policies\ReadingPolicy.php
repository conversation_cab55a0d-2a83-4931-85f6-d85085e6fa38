<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Reading;

class ReadingPolicy
{
    /**
     * المحصل فقط يملك صلاحية إنشاء قراءة جديدة.
     */
    public function create(User $user)
    {
        return $user->role === 'محصل';
    }

    /**
     * المراجع فقط يستطيع الموافقة على القراءة.
     */
    public function approve(User $user, Reading $reading)
    {
        return $user->role === 'مراجع';
    }

    /**
     * المراجع فقط يستطيع رفض القراءة.
     */
    public function reject(User $user, Reading $reading)
    {
        return $user->role === 'مراجع';
    }

    /**
     * المدير والمراجع يمكنهم مشاهدة التفاصيل.
     */
    public function view(User $user, Reading $reading)
    {
        return in_array($user->role, ['مدير', 'مراجع']);
    }

    /**
     * المدير فقط يمكنه حذف القراءة.
     */
    public function delete(User $user, Reading $reading)
    {
        return $user->role === 'مدير';
    }

    /**
     * المحصل يمكنه تعديل القراءة فقط إذا كانت معلقة.
     * المدير يمكنه تعديل أي قراءة.
     */
    public function update(User $user, Reading $reading)
    {
        if ($user->role === 'محصل' && $reading->status === 'pending') {
            return true;
        }

        return $user->role === 'مدير';
    }
}
