<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Subscriber;

class SubscriberPolicy
{
    public function viewAny(User $user)
    {
        return in_array($user->role, ['مدير', 'مراجع']);
    }

    public function view(User $user, Subscriber $subscriber)
    {
        return in_array($user->role, ['مدير', 'مراجع']);
    }

    public function create(User $user)
    {
        return $user->role === 'مدير';
    }

    public function update(User $user, Subscriber $subscriber)
    {
        return $user->role === 'مدير';
    }

    public function delete(User $user, Subscriber $subscriber)
    {
        return $user->role === 'مدير';
    }
}
