<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Bill;

class BillPolicy
{
    public function viewAny(User $user)
    {
        // المدير، المراجع، والمحصل (مثلاً) ممكن يشوفوا قائمة الفواتير
        return in_array($user->role, ['مدير', 'مراجع', 'محصل']);
    }

    public function view(User $user, Bill $bill)
    {
        // المدير والمراجع يقدروا يشوفوا كل الفواتير
        if (in_array($user->role, ['مدير', 'مراجع'])) {
            return true;
        }

        // المحصل يمكنه رؤية الفواتير المرتبطة بمنطقته (مثلاً)
        if ($user->role === 'محصل') {
            return $bill->reading->subscriber->zone->collector_id === $user->id;
        }

        return false;
    }

    public function create(User $user)
    {
        // فقط المراجع يصدر الفاتورة
        return $user->role === 'مراجع';
    }

    public function update(User $user, Bill $bill)
    {
        // المدير فقط يمكنه تعديل الفاتورة (كمثال)
        return $user->role === 'مدير';
    }

    public function markAsPaid(User $user, Bill $bill)
    {
        // المدير والمحصل يمكنهم تحديد الفاتورة كمدفوعة (حسب النظام)
        return in_array($user->role, ['مدير', 'محصل']);
    }

    public function delete(User $user, Bill $bill)
    {
        // المدير فقط يمكنه حذف الفاتورة
        return $user->role === 'مدير';
    }
}
