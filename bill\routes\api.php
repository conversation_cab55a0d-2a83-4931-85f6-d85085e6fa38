<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BillController;
use App\Http\Controllers\SubscriberController;
use App\Http\Controllers\ReadingController;
use App\Http\Controllers\ZoneController;
use App\Http\Controllers\DashboardController;

// المسارات العامة (غير محمية)
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// المسارات المحمية (تتطلب تسجيل دخول)
Route::middleware('auth:sanctum')->group(function () {
    // تسجيل الخروج
    Route::post('/logout', [AuthController::class, 'logout']);

    // بيانات المستخدم الحالي
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // إدارة المناطق - فقط للمسؤول
    Route::middleware('role:admin')->apiResource('zones', ZoneController::class);

    // إدارة المشتركين
    // عرض المشتركين متاح للجميع
    Route::get('subscribers', [SubscriberController::class, 'index']);
    Route::get('subscribers/{subscriber}', [SubscriberController::class, 'show']);

    // فقط المسؤول يمكنه إنشاء وتعديل وحذف المشتركين
    Route::middleware('role:admin')->group(function () {
        Route::post('subscribers', [SubscriberController::class, 'store']);
        Route::put('subscribers/{subscriber}', [SubscriberController::class, 'update']);
        Route::delete('subscribers/{subscriber}', [SubscriberController::class, 'destroy']);
    });

    // إدارة القراءات - مع التحقق من صلاحيات معينة في الـPolicy
    Route::apiResource('readings', ReadingController::class);

    Route::get('/readings-pending', [ReadingController::class, 'pending']);

    Route::put('/readings/{reading}/approve', [ReadingController::class, 'approve'])
        ->middleware('can:approve,reading');

    Route::put('/readings/{reading}/reject', [ReadingController::class, 'reject'])
        ->middleware('can:reject,reading');

    // إدارة الفواتير
    Route::apiResource('bills', BillController::class);

    Route::post('/bills/generate', [BillController::class, 'generateBills'])
        ->middleware('can:create,App\Models\Bill');

    Route::put('/bills/{bill}/mark-paid', [BillController::class, 'markAsPaid'])
        ->middleware('can:markAsPaid,bill');

    Route::get('/bills-overdue', [BillController::class, 'overdue']);

    // Dashboard routes
    Route::prefix('dashboard')->group(function () {
        Route::get('/stats', [DashboardController::class, 'stats']);
        Route::get('/trends', [DashboardController::class, 'trends']);
        Route::get('/consumption', [DashboardController::class, 'consumption']);
        Route::get('/zone-performance', [DashboardController::class, 'zonePerformance'])
            ->middleware('role:admin');
    });
});
