import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'auth_service.dart';
import 'local_db_service.dart';

class ApiService {
  static const String baseUrl = 'http://10.0.2.2:8000/api';
  static const String ocrUrl = 'http://10.0.2.2:5000';
  final AuthService _authService = AuthService();
  final LocalDbService _localDbService = LocalDbService.instance;
  final Connectivity _connectivity = Connectivity();

  // Helper method for common request handling
  Future<Map<String, dynamic>?> _handleRequest(
    Future<http.Response> request,
    String endpoint,
  ) async {
    try {
      if (!await _checkConnectivity()) return null;

      final response = await request;
      final responseBody = utf8.decode(response.bodyBytes);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(responseBody);
      } else {
        _logError(endpoint, response.statusCode, responseBody);
        return null;
      }
    } catch (e) {
      _logError(endpoint, 0, e.toString());
      return null;
    }
  }

  Future<bool> _checkConnectivity() async {
    final result = await _connectivity.checkConnectivity();
    if (result == ConnectivityResult.none) {
      _logError('Connectivity', 0, 'No internet connection');
      return false;
    }
    return true;
  }

  void _logError(String endpoint, int statusCode, String error) {
    if (kDebugMode) {
      debugPrint('API Error: $endpoint | Status: $statusCode | Error: $error');
    }
  }

  // GET request
  Future<Map<String, dynamic>?> get(String endpoint) async {
    return _handleRequest(
      http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: await _authService.getAuthHeaders(),
      ),
      'GET $endpoint',
    );
  }

  // POST request
  Future<Map<String, dynamic>?> post(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    return _handleRequest(
      http.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: await _authService.getAuthHeaders(),
        body: jsonEncode(data),
      ),
      'POST $endpoint',
    );
  }

  // PUT request
  Future<Map<String, dynamic>?> put(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    return _handleRequest(
      http.put(
        Uri.parse('$baseUrl$endpoint'),
        headers: await _authService.getAuthHeaders(),
        body: jsonEncode(data),
      ),
      'PUT $endpoint',
    );
  }

  // DELETE request
  Future<bool> delete(String endpoint) async {
    try {
      if (!await _checkConnectivity()) return false;

      final response = await http.delete(
        Uri.parse('$baseUrl$endpoint'),
        headers: await _authService.getAuthHeaders(),
      );
      return response.statusCode == 200;
    } catch (e) {
      _logError('DELETE $endpoint', 0, e.toString());
      return false;
    }
  }

  // Image upload for OCR
  Future<Map<String, dynamic>?> uploadImageForOCR(File imageFile) async {
    try {
      if (!await _checkConnectivity()) return null;

      var request = http.MultipartRequest('POST', Uri.parse('$ocrUrl/ocr'))
        ..files.add(await http.MultipartFile.fromPath('image', imageFile.path));

      var response = await request.send();
      var responseData = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        return jsonDecode(responseData);
      }
      _logError('OCR Upload', response.statusCode, responseData);
      return null;
    } catch (e) {
      _logError('OCR Upload', 0, e.toString());
      return null;
    }
  }

  // POST with image
  Future<Map<String, dynamic>?> postWithImage(
    String endpoint,
    Map<String, dynamic> data,
    File? imageFile,
  ) async {
    try {
      if (!await _checkConnectivity()) return null;

      var request = http.MultipartRequest('POST', Uri.parse('$baseUrl$endpoint'))
        ..headers.addAll(await _authService.getAuthHeaders());

      data.forEach((key, value) {
        if (value != null) request.fields[key] = value.toString();
      });

      if (imageFile != null) {
        request.files.add(
          await http.MultipartFile.fromPath('image', imageFile.path),
        );
      }

      var response = await request.send();
      var responseData = await response.stream.bytesToString();

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(responseData);
      }
      _logError('POST with Image', response.statusCode, responseData);
      return null;
    } catch (e) {
      _logError('POST with Image', 0, e.toString());
      return null;
    }
  }

  // ========== Domain-Specific Methods ==========

  Future<List<dynamic>?> getZones() async {
    final result = await get('/zones');
    return result?['data'];
  }

  Future<Map<String, dynamic>?> createZone(String name, int? collectorId) async {
    return post('/zones', {
      'name': name,
      'collector_id': collectorId,
    });
  }

  Future<List<dynamic>?> getSubscribers({int? zoneId}) async {
    final result = await get(
      zoneId != null ? '/subscribers?zone_id=$zoneId' : '/subscribers',
    );
    return result?['data'];
  }

  Future<List<dynamic>?> getReadings({String? status}) async {
    final result = await get(
      status != null ? '/readings?status=$status' : '/readings',
    );
    return result?['data'];
  }

  Future<List<dynamic>?> getBills({bool? isPaid, bool? overdue}) async {
    String endpoint = '/bills';
    final params = {
      if (isPaid != null) 'is_paid': isPaid.toString(),
      if (overdue == true) 'overdue': '1',
    };
    if (params.isNotEmpty) {
      endpoint += '?${Uri(queryParameters: params).query}';
    }
    final result = await get(endpoint);
    return result?['data'];
  }

  Future<List<dynamic>> getUsers() async {
    try {
      if (await _checkConnectivity()) {
        final result = await get('/users');
        if (result != null && result['data'] != null) {
          await _localDbService.insertUsers(
            List<Map<String, dynamic>>.from(result['data']),
          );
          return result['data'];
        }
      }
      return await _localDbService.getUsers();
    } catch (e) {
      _logError('getUsers', 0, e.toString());
      return await _localDbService.getUsers();
    }
  }

  // إضافة قراءة جديدة
  Future<Map<String, dynamic>?> createReading(Map<String, dynamic> data, File? image) async {
    return await postWithImage('/readings', data, image);
  }

  // جلب القراءات المعلقة
  Future<List<dynamic>?> getPendingReadings() async {
    final result = await get('/readings-pending');
    return result?['data'];
  }

  // اعتماد قراءة
  Future<Map<String, dynamic>?> approveReading(int readingId, String? note) async {
    return await put('/readings/$readingId/approve', {
      if (note != null) 'review_note': note,
    });
  }

  // رفض قراءة
  Future<Map<String, dynamic>?> rejectReading(int readingId, String note) async {
    return await put('/readings/$readingId/reject', {
      'review_note': note,
    });
  }

  // إضافة مشترك جديد
  Future<Map<String, dynamic>?> createSubscriber(Map<String, dynamic> data) async {
    return await post('/subscribers', data);
  }

  // تحديد الفاتورة كمدفوعة
  Future<Map<String, dynamic>?> markBillAsPaid(int billId) async {
    return await put('/bills/$billId/mark-paid', {});
  }

  // توليد الفواتير تلقائياً
  Future<Map<String, dynamic>?> generateBills() async {
    return await post('/bills/generate', {});
  }
}
