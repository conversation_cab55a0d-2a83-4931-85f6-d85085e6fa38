<?php

namespace App\Http\Controllers;

use App\Models\Reading;
use App\Models\Subscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ReadingController extends Controller
{
    // عرض جميع القراءات مع الفلاتر المناسبة
    public function index(Request $request)
    {
        $query = Reading::with(['subscriber', 'collector', 'reviewer']);

        // فلترة حسب الحالة (مثلاً: pending, approved, rejected)
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // إذا كان المستخدم محصل، يعرض فقط قراءاته
        if ($request->user()->isCollector()) {
            $query->where('collector_id', $request->user()->id);
        }

        // إذا كان المراجع، يعرض قراءاته التي راجعها أو القراءات المعلقة
        if ($request->user()->isReviewer()) {
            $query->where(function ($q) use ($request) {
                $q->where('reviewed_by', $request->user()->id)
                  ->orWhere('status', 'pending');
            });
        }

        $readings = $query->latest()->get();
        return response()->json($readings);
    }

    // إضافة قراءة جديدة
    public function store(Request $request)
    {
        $request->validate([
            'subscriber_id' => 'required|exists:subscribers,id',
            'previous_reading' => 'required|numeric|min:0',
            'current_reading' => 'required|numeric|min:0|gte:previous_reading',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $subscriber = Subscriber::find($request->subscriber_id);

        // تحقق من أن المحصل ينتمي لنفس المنطقة
        if ($request->user()->isCollector()) {
            $collectorZones = $request->user()->zones->pluck('id');
            if (!$collectorZones->contains($subscriber->zone_id)) {
                return response()->json([
                    'message' => 'لا يمكنك إضافة قراءة لمشترك خارج منطقتك'
                ], 403);
            }
        }

        $data = $request->validated();
        $data['collector_id'] = $request->user()->id;

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('readings', 'public');
            $data['image_path'] = $imagePath;
        }

        $reading = Reading::create($data);
        $reading->load(['subscriber', 'collector']);

        return response()->json($reading, 201);
    }

    // عرض قراءة معينة
    public function show(Reading $reading)
    {
        $reading->load(['subscriber', 'collector', 'reviewer', 'bill']);
        return response()->json($reading);
    }

    // تعديل قراءة (إذا كانت الحالة معلقة فقط)
    public function update(Request $request, Reading $reading)
    {
        if ($reading->status !== 'pending') {
            return response()->json([
                'message' => 'لا يمكن تعديل قراءة تم مراجعتها'
            ], 422);
        }

        $request->validate([
            'previous_reading' => 'required|numeric|min:0',
            'current_reading' => 'required|numeric|min:0|gte:previous_reading',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $data = $request->validated();

        if ($request->hasFile('image')) {
            if ($reading->image_path) {
                Storage::disk('public')->delete($reading->image_path);
            }
            $imagePath = $request->file('image')->store('readings', 'public');
            $data['image_path'] = $imagePath;
        }

        $reading->update($data);
        $reading->load(['subscriber', 'collector']);

        return response()->json($reading);
    }

    // حذف قراءة (إذا كانت الحالة معلقة فقط)
    public function destroy(Reading $reading)
    {
        if ($reading->status !== 'pending') {
            return response()->json([
                'message' => 'لا يمكن حذف قراءة تم مراجعتها'
            ], 422);
        }

        if ($reading->image_path) {
            Storage::disk('public')->delete($reading->image_path);
        }

        $reading->delete();
        return response()->json(['message' => 'تم حذف القراءة بنجاح']);
    }

    // عرض القراءات المعلقة فقط
    public function pending()
    {
        $readings = Reading::with(['subscriber', 'collector'])
                          ->where('status', 'pending')
                          ->latest()
                          ->get();
        return response()->json($readings);
    }

    // اعتماد القراءة (تغيير الحالة إلى approved)
    public function approve(Request $request, Reading $reading)
    {
        if (!$request->user()->isReviewer() && !$request->user()->isAdmin()) {
            return response()->json([
                'message' => 'غير مصرح لك بمراجعة القراءات'
            ], 403);
        }

        $reading->update([
            'status' => 'approved',
            'reviewed_by' => $request->user()->id,
            'review_note' => $request->review_note,
        ]);

        $reading->load(['subscriber', 'collector', 'reviewer']);
        return response()->json($reading);
    }

    // رفض القراءة (تغيير الحالة إلى rejected)
    public function reject(Request $request, Reading $reading)
    {
        if (!$request->user()->isReviewer() && !$request->user()->isAdmin()) {
            return response()->json([
                'message' => 'غير مصرح لك بمراجعة القراءات'
            ], 403);
        }

        $request->validate([
            'review_note' => 'required|string',
        ]);

        $reading->update([
            'status' => 'rejected',
            'reviewed_by' => $request->user()->id,
            'review_note' => $request->review_note,
        ]);

        $reading->load(['subscriber', 'collector', 'reviewer']);
        return response()->json($reading);
    }
}
