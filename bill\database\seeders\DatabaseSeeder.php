<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::create([
            'name' => 'Admin User',
            'username' => 'admin',
            'password' => Hash::make(value: 'password'),
            'role' => 'مدير',
        ]);

        User::create([
            'name' => 'Reviewer User',
            'username' => 'reviewer',
            'password' => Hash::make('password'),
            'role' => 'مراجع',
        ]);

        User::create([
            'name' => 'Collector User',
            'username' => 'collector',
            'password' => Hash::make('password'),
            'role' => 'محصل',
        ]);
    }
}
