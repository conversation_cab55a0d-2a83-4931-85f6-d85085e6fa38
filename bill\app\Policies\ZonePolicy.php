<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Zone;

class ZonePolicy
{
    public function viewAny(User $user)
    {
        return true; // الكل يستطيع رؤية المناطق
    }

    public function view(User $user, Zone $zone)
    {
        return true; // الكل يستطيع مشاهدة أي منطقة
    }

    public function create(User $user)
    {
        return $user->role === 'admin'; // فقط المدير (admin) يستطيع الإنشاء
    }

    public function update(User $user, Zone $zone)
    {
        return $user->role === 'admin'; // فقط المدير (admin) يستطيع التعديل
    }

    public function delete(User $user, Zone $zone)
    {
        return $user->role === 'admin'; // فقط المدير (admin) يستطيع الحذف
    }
}
